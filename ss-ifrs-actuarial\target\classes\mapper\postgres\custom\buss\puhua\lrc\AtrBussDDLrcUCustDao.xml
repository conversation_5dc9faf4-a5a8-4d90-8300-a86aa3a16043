<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- This file was generated by GIS MyBatis Generator(v1.2.12) -->
<!-- This file was generated on : 2023-02-10 16:34:25 -->
<!-- Copyright (c) 2017-2027, CHINA GIS INSURANCE GROUP LTD. All Rights Reserved. -->

<mapper namespace="com.ss.ifrs.actuarial.dao.buss.puhua.lrc.AtrBussDDLrcUDao">
  <!-- 本配置文件由GIS MyBatis Generator(v1.2.12)工具自动生成，用于添加自定义内容！ -->
  <!-- 基础配置(**IDao.xml)中定义的所有节点均可在自定义配置(**CustDao.xml)中直接引用（包括缓存节点），请勿重复添加！ -->
  <resultMap id="CustResultMap" type="com.ss.ifrs.actuarial.pojo.atrbuss.vo.AtrBussDDLrcIcuCalcVo">
    <id column="ID" property="id" jdbcType="DECIMAL" />
    <result column="ACTION_NO" property="actionNo" jdbcType="VARCHAR" />
    <result column="TASK_CODE" property="taskCode" jdbcType="VARCHAR" />
    <result column="CALC_TYPE" property="calcType" jdbcType="VARCHAR" />
    <result column="DATA_KEY" property="dataKey" jdbcType="DECIMAL" />
    <result column="entity_id" property="entityId" jdbcType="DECIMAL" />
    <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR" />
    <result column="ENDORSE_SEQ_NO" property="endorseSeqNo" jdbcType="VARCHAR" />
    <result column="CURRENCY_CODE" property="currencyCode" jdbcType="VARCHAR" />
    <result column="YEAR_MONTH" property="yearMonth" jdbcType="VARCHAR" />
    <result column="PORTFOLIO_NO" property="portfolioNo" jdbcType="VARCHAR" />
    <result column="ICG_NO" property="icgNo" jdbcType="VARCHAR" />
    <result column="EVALUATE_APPROACH" property="evaluateApproach" jdbcType="VARCHAR" />
    <result column="LOA_CODE" property="loaCode" jdbcType="VARCHAR" />
    <result column="CMUNIT_NO" property="cmunitNo" jdbcType="VARCHAR" />
    <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR" />
    <result column="EVALUATE_DATE" property="evaluateDate" jdbcType="TIMESTAMP" />
    <result column="CONTRACT_DATE" property="contractDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_IN_DATE" property="effectiveDateInDate" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_IN_DATE" property="expiryDateInDate" jdbcType="TIMESTAMP" />
    <result column="EFFECTIVE_DATE_BOM" property="effectiveDateBom" jdbcType="TIMESTAMP" />
    <result column="EXPIRY_DATE_EOM" property="expiryDateEom" jdbcType="TIMESTAMP" />
    <result column="GROSS_PREMIUM" property="grossPremium" jdbcType="DECIMAL" />
    <result column="COVERAGE_AMOUNT" property="coverageAmount" jdbcType="DECIMAL" />
    <result column="FACULTATIVE_IS" property="facultativeIs" jdbcType="VARCHAR" />
    <result column="PASSED_DATES" property="passedDates" jdbcType="DECIMAL" />
    <result column="PASSED_MONTHS" property="passedMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS" property="remainingMonths" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_PE" property="remainingPremTermPe" jdbcType="DECIMAL" />
    <result column="REMAINING_MONTHS_FUTURE" property="remainingMonthsFuture" jdbcType="DECIMAL" />
    <result column="REMAINING_PREM_TERM_CB" property="remainingPremTermCb" jdbcType="DECIMAL" />
    <result column="PAYMENT_QUARTER" property="paymentQuarter" jdbcType="DECIMAL" />
    <result column="ed_premium_per_coverage_day" property="edPremiumPerCoverageDay" jdbcType="DECIMAL" />
    <result column="ED_PREMIUM" property="edPremium" jdbcType="DECIMAL" />
    <result column="REMAINING_QUARTERS" property="remainingQuarters" jdbcType="DECIMAL" />
    <result column="PRI_CUR_END_REMAIN_CSM_RATE" property="priCurEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="P_UNTIL_REPORT_REMAIN_CSM_RATE" property="priUntilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="CUMULATIVE_ED_RATE" property="cumulativeEdRate" jdbcType="DECIMAL" />
    <result column="CUR_END_REMAIN_CSM_RATE" property="curEndRemainCsmRate" jdbcType="DECIMAL" />
    <result column="UNTIL_REPORT_REMAIN_CSM_RATE" property="untilReportRemainCsmRate" jdbcType="DECIMAL" />
    <result column="DEPT_ID" property="deptId" jdbcType="DECIMAL"/>
    <result column="ELR" property="elr" jdbcType="DECIMAL"/>

      <result column="entity_code" property="entityCode" jdbcType="VARCHAR" />
    <result column="entity_c_name" property="entityCName" jdbcType="VARCHAR" />
    <result column="entity_l_name" property="entityLName" jdbcType="VARCHAR" />
    <result column="entity_e_name" property="entityEName" jdbcType="VARCHAR" />
  </resultMap>
  <!-- 通用查询结果列-->
  <sql id="Cust_Column_List">
    a.ID, a.TASK_CODE, a.CALC_TYPE, a.DATA_KEY, a.entity_id, a.POLICY_NO, a.ENDORSE_SEQ_NO, a.currency_code,
    a.YEAR_MONTH, a.PORTFOLIO_NO, a.ICG_NO, a.EVALUATE_APPROACH,  a.LOA_CODE, a.CMUNIT_NO,
    a.PRODUCT_CODE, a.EVALUATE_DATE, a.CONTRACT_DATE, a.EFFECTIVE_DATE_IN_DATE, a.APPROVAL_DATE_IN_DATE,
    a.EXPIRY_DATE_IN_DATE, a.EFFECTIVE_DATE_BOM, a.EXPIRY_DATE_EOM, a.payment_frequency_code, a.payment_frequency_no,
    a.GROSS_PREMIUM, a.COVERAGE_AMOUNT, a.FACULTATIVE_IS, a.PASSED_DATES, a.PASSED_MONTHS, a.REMAINING_MONTHS,
    a.REMAINING_PREM_TERM_PE, a.REMAINING_MONTHS_FUTURE, a.REMAINING_PREM_TERM_CB, a.PAYMENT_QUARTER,
    a.ed_premium_per_coverage_day, a.ED_PREMIUM, a.PRI_CUR_END_REMAIN_CSM_RATE,
    a.P_UNTIL_REPORT_REMAIN_CSM_RATE, a.CUMULATIVE_ED_RATE, a.CUR_END_REMAIN_CSM_RATE, a.UNTIL_REPORT_REMAIN_CSM_RATE,
    a.DEPT_ID, a.ELR
    c.entity_e_name, c.entity_c_name, c.entity_l_name
  </sql>

    <select id="countDateByVo" fetchSize="2000" flushCache="false" useCache="true" resultType="Long"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
        count(a.ACTION_NO) as "actionNo"
        from atruser.ATR_BUSS_DD_LRC_U a
        where a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
    </select>


    <select id="findLrcDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            <include refid="Fuzzy_Language_Column"/>
            a.ACTION_NO as "actionNo",
            a.POLICY_NO as "policyNo",
            a.ENDORSE_SEQ_NO as "endorseSeqNo",
            a.YEAR_MONTH as "yearMonth",
            a.PORTFOLIO_NO as "portfolioNo",
            a.ICG_NO as "icgNo",
            a.CMUNIT_NO as "cmunitNo",
            a.kind_code as "kindCode",
            a.risk_code as "riskCode",
            a.company_code1 as "companyCode1",
            a.company_code2 as "companyCode2",
            a.company_code3 as "companyCode3",
            a.company_code4 as "companyCode4",
            to_char((to_date(a.YEAR_MONTH, 'YYYYMM') + interval '1 month' - interval '1 day'), 'YYYY/MM/DD') as "evaluateDate",
            to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
            to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
            to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
            to_char(a.approval_date,'yyyy/mm/dd') as "approvalDate",
            to_char(a.comfirm_date,'yyyy/mm/dd') as "confirmDate",
            to_char(a.issue_date,'yyyy/mm/dd') as "issueDate",
            a.icg_name as "icgNoName",
            a.pl_judge_rslt as "plJudgeRslt",
            a.premium as "premium",
            a.pre_cuml_ed_premium as "preCumlEdPremium",
            a.pre_cuml_ed_net_fee as "preCumlEdNetFee",
            a.pre_cuml_paid_premium as "preCumlPaidPremium",
            a.pre_cuml_paid_net_fee as "preCumlPaidNetFee",
            a.pre_cuml_paid_iacf as "preCumlPaidIacf",
            c.entity_code as "entityCode",
            c.entity_c_name as "entityCName",
            a.net_fee as "netFee",
            'CNY' as "currencyCode",
            'PAA' as "evaluateApproach",
            a.net_fee_rate as "feeRate",
            a.iacf as "iacf",
            a.pre_cuml_ed_iacf as "preCumlEdIacf",
            dev_data.badDebt as "badDebt",
            a.pre_cuml_ed_iaehc_in                  as "preCumlEdIaehcIn",
            a.pre_cuml_ed_iaehc_out                 as "preCumlEdIaehcOut",
            (a.iaehc_in + a.pre_iaehc_in)           as "iaehcIn",
            (a.iaehc_out + a.pre_iaehc_out)         as "iaehcOut"
            <if test="null != devNoList and devNoList.size > 0">
                <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                    dev_data."${item}" AS "${item}"
                </foreach>
            </if>
        FROM atruser.ATR_BUSS_DD_LRC_U a
        LEFT JOIN (
            SELECT 
                dev.main_id,
                MAX(case when dev.dev_no = 0 then dev.bad_debt end) as badDebt
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                </if>
            FROM atruser.ATR_BUSS_DD_LRC_U_DEV dev
            INNER JOIN atruser.ATR_BUSS_DD_LRC_U m ON dev.main_id = m.id
            WHERE m.action_no = #{actionNo,jdbcType=VARCHAR}
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
                <if test="riskClassCode != null and riskClassCode != ''">
                    AND m.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        LEFT JOIN bpluser.bbs_conf_entity c ON a.entity_id = c.entity_id
        <include refid="FindByCodeIdx"/>
        WHERE a.action_no = #{actionNo,jdbcType=VARCHAR}
        <if test="portfolioNo != null and portfolioNo != ''">
            AND a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            AND a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            AND a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>
        ORDER BY a.id
    </select>

    <select id="countLrcUDetail" resultType="Long">
        select count(1)
        FROM atr_buss_lrc_action ablc
        left join  atruser.ATR_BUSS_DD_LRC_U a on ablc.action_no= a.action_no
        where ablc.confirm_is = '1'
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            and ablc.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="entityId != null">
            and ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="yearMonthStart != null ">
            and ablc.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthEnd != null ">
            and ablc.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            and a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            and a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            and a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="findLrcHandelDetailPage" fetchSize="1000" flushCache="false" useCache="true" resultType="java.util.LinkedHashMap"  parameterType="com.ss.ifrs.actuarial.pojo.atrdap.vo.AtrDapDrawVo">
        select
            <include refid="Fuzzy_Language_Column"/>
            a.ACTION_NO as "actionNo",
            a.POLICY_NO as "policyNo",
            a.ENDORSE_SEQ_NO as "endorseSeqNo",
            a.YEAR_MONTH as "yearMonth",
            a.PORTFOLIO_NO as "portfolioNo",
            a.ICG_NO as "icgNo",
            a.CMUNIT_NO as "cmunitNo",
            a.kind_code as "kindCode",
            a.risk_code as "riskCode",
            a.company_code1 as "companyCode1",
            a.company_code2 as "companyCode2",
            a.company_code3 as "companyCode3",
            a.company_code4 as "companyCode4",
            to_char((to_date(a.YEAR_MONTH, 'YYYYMM') + interval '1 month' - interval '1 day'), 'YYYY/MM/DD') as "evaluateDate",
            to_char(a.CONTRACT_DATE,'yyyy/mm/dd') as "contractDate",
            to_char(a.effective_date,'yyyy/mm/dd') as "effectiveDate",
            to_char(a.expiry_date,'yyyy/mm/dd') as "expiryDate",
            to_char(a.approval_date,'yyyy/mm/dd') as "approvalDate",
            to_char(a.comfirm_date,'yyyy/mm/dd') as "confirmDate",
            to_char(a.issue_date,'yyyy/mm/dd') as "issueDate",
            a.icg_name as "icgNoName",
            a.pl_judge_rslt as "plJudgeRslt",
            a.premium as "premium",
            a.pre_cuml_ed_premium as "preCumlEdPremium",
            a.pre_cuml_ed_net_fee as "preCumlEdNetFee",
            a.pre_cuml_paid_premium as "preCumlPaidPremium",
            a.pre_cuml_paid_net_fee as "preCumlPaidNetFee",
            a.pre_cuml_paid_iacf as "preCumlPaidIacf",
            c.entity_code as "entityCode",
            c.entity_c_name as "entityCName",
            a.net_fee as "netFee",
            'CNY' as "currencyCode",
            'PAA' as "evaluateApproach",
            a.net_fee_rate as "feeRate",
            a.iacf as "iacf",
            a.pre_cuml_ed_iacf as "preCumlEdIacf",
            dev_data.badDebt as "badDebt",
            a.pre_cuml_ed_iaehc_in                  as "preCumlEdIaehcIn",
            a.pre_cuml_ed_iaehc_out                 as "preCumlEdIaehcOut",
            (a.iaehc_in + a.pre_iaehc_in)           as "iaehcIn",
            (a.iaehc_out + a.pre_iaehc_out)         as "iaehcOut"
            <if test="null != devNoList and devNoList.size > 0">
                <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                    dev_data."${item}" AS "${item}"
                </foreach>
            </if>
        FROM atr_buss_lrc_action ablc
        LEFT JOIN atruser.ATR_BUSS_DD_LRC_U a ON ablc.action_no = a.action_no
        LEFT JOIN (
            SELECT
                dev.main_id,
                MAX(case when dev.dev_no = 0 then dev.bad_debt end) as badDebt
                <if test="null != devNoList and devNoList.size > 0">
                    <foreach collection="devNoList" item="item" index="index" open=","  separator=",">
                         MAX(CASE dev.DEV_NO WHEN ${item} THEN dev.${feeType} ELSE NULL END) AS "${item}"
                    </foreach>
                </if>
            FROM atruser.ATR_BUSS_DD_LRC_U_DEV dev
            INNER JOIN atruser.ATR_BUSS_DD_LRC_U m ON dev.main_id = m.id
            INNER JOIN atr_buss_lrc_action ablc_sub ON m.action_no = ablc_sub.action_no
            WHERE ablc_sub.confirm_is = '1'
                <if test="entityId != null">
                    AND ablc_sub.entity_id = #{entityId,jdbcType=BIGINT}
                </if>
                <if test="businessSourceCode != null and businessSourceCode != ''">
                    AND ablc_sub.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthStart != null ">
                    AND ablc_sub.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
                </if>
                <if test="yearMonthEnd != null ">
                    AND ablc_sub.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
                </if>
                <if test="portfolioNo != null and portfolioNo != ''">
                    AND m.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
                </if>
                <if test="icgNo != null and icgNo != ''">
                    AND m.icg_no = #{icgNo,jdbcType=VARCHAR}
                </if>
                <if test="riskClassCode != null and riskClassCode != ''">
                    AND m.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
                </if>
            GROUP BY dev.main_id
        ) dev_data ON a.id = dev_data.main_id
        LEFT JOIN bpluser.bbs_conf_entity c ON a.entity_id = c.entity_id
        <include refid="FindByCodeIdx"/>
        WHERE ablc.confirm_is = '1'
        <if test="entityId != null">
            AND ablc.entity_id = #{entityId,jdbcType=BIGINT}
        </if>
        <if test="businessSourceCode != null and businessSourceCode != ''">
            AND ablc.BUSINESS_SOURCE_CODE = #{businessSourceCode,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthStart != null ">
            AND ablc.YEAR_MONTH &gt;= #{yearMonthStart,jdbcType=VARCHAR}
        </if>
        <if test="yearMonthEnd != null ">
            AND ablc.YEAR_MONTH &lt;= #{yearMonthEnd,jdbcType=VARCHAR}
        </if>
        <if test="portfolioNo != null and portfolioNo != ''">
            AND a.portfolio_no = #{portfolioNo,jdbcType=VARCHAR}
        </if>
        <if test="icgNo != null and icgNo != ''">
            AND a.icg_no = #{icgNo,jdbcType=VARCHAR}
        </if>
        <if test="riskClassCode != null and riskClassCode != ''">
            AND a.risk_class_code = #{riskClassCode,jdbcType=VARCHAR}
        </if>
        ORDER BY a.id
    </select>



    <sql id="Fuzzy_Language_Column">
        <choose>
            <when test="language != null and language != '' ">
                <choose>
                    <when test='language == "zh"'>
                        c4.code_c_name as "businessSourceCode",
                    </when>
                    <when test='language == "tn"'>
                        c4.code_l_name as "businessSourceCode",
                    </when>
                    <when test='language == "en"'>
                        c4.code_e_name as "businessSourceCode",
                    </when>
                    <otherwise>
                        c4.code_e_name as "businessSourceCode",
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                a.business_type AS "businessSourceCode",
            </otherwise>
        </choose>
    </sql>
    <sql id="FindByCodeIdx">
        <if test="language != null and language != '' ">
           left join bpluser.bpl_v_conf_code c4 ON c4.code_code_idx = 'BusinessType/Base/' || a.business_type
        </if>
    </sql>
    <sql id="Group_Language_Column">
        <choose>
            <when test="language != null and language != '' ">
                <choose>
                    <when test='language == "zh"'>
                        c4.code_c_name,
                    </when>
                    <when test='language == "tn"'>
                        c4.code_l_name,
                    </when>
                    <when test='language == "en"'>
                        c4.code_e_name,
                    </when>
                    <otherwise>
                        c4.code_e_name,
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                a.business_type,
            </otherwise>
        </choose>
    </sql>

</mapper>
